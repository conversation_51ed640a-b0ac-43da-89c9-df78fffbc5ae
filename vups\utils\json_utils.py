import re


def fix_json_format(raw_str):
    # First fix the quotes inside text values
    fixed = re.sub(r"'([^']*)'", r'"\1"', raw_str)  # Convert single quotes inside text to double quotes
    fixed = re.sub(r"(\w+)\s*:", r'"\1":', fixed)
    fixed = re.sub(r"'", '"', fixed)
    fixed = re.sub(r",\s*([\]}])", r"\1", fixed)  # Remove trailing commas
    fixed = re.sub(r"\bTrue\b", "true", fixed)  # Convert Python booleans
    fixed = re.sub(r"\bFalse\b", "false", fixed)
    fixed = re.sub(r"\bNone\b", "null", fixed)  # Convert Python None
    return fixed

