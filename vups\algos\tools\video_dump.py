import asyncio
import json
import logging
import os
import re
import subprocess
from contextlib import contextmanager
import sys
from typing import List

import aiohttp
from bilibili_api import video, Credential, HEADERS, get_client
from vups.algos.tools.asr import AliParaformerASR, BCutASR, BaseASR
from vups.config import VUPS_PROJECT_ROOT
from vups.utils.json_utils import fix_json_format
from vups_server.base.cookie_manager import get_cookie_field
from vups.logger import logger


# Configuration constants
SESSDATA = get_cookie_field("user", "SESSDATA")
BILI_JCT = get_cookie_field("user", "bili_jct")
BUVID3 = get_cookie_field("user", "buvid3")

if sys.platform == "win32":
    FFMPEG_PATH = VUPS_PROJECT_ROOT / "assets/ffmpeg/ffmpeg.exe"
elif sys.platform == "linux":
    FFMPEG_PATH = VUPS_PROJECT_ROOT / "assets/ffmpeg/ffmpeg"
else:
    raise NotImplementedError(f"Unsupported platform: {sys.platform}")

# Default file names
DEFAULT_OUTPUT_FILENAME = "video.mp4"
FLV_TEMP_FILENAME = "flv_temp.flv"
VIDEO_TEMP_FILENAME = "video_temp.m4s"
AUDIO_TEMP_FILENAME = "audio_temp.m4s"
AUDIO_OUTPUT_FILENAME = "test_audio.mp3"
TRANSCRIPT_OUTPUT_FILENAME = "test_subtitle.json"


class VideoDownloadError(Exception):
    """Custom exception for video download errors."""
    pass


class FFmpegError(Exception):
    """Custom exception for FFmpeg processing errors."""
    pass


@contextmanager
def temporary_files(*filenames: str):
    """Context manager for handling temporary files with automatic cleanup."""
    temp_files = []
    try:
        for filename in filenames:
            temp_files.append(filename)
        yield temp_files
    finally:
        for temp_file in temp_files:
            try:
                if os.path.exists(temp_file):
                    os.remove(temp_file)
                    logger.debug(f"Cleaned up temporary file: {temp_file}")
            except OSError as e:
                logger.warning(f"Failed to remove temporary file {temp_file}: {e}")


class VideoSourceDownloader:
    """Handles downloading and processing of Bilibili videos."""

    def __init__(self, credential: Credential = None, asr: str = "ali"):
        """Initialize the video downloader.

        Args:
            credential: Bilibili API credentials

        Raises:
            ValueError: If bvid is empty or invalid
        """
        if not credential:
            self.credential = create_credential()
        else:
            self.credential = credential

        if asr == "ali":
            self.asr = AliParaformerASR(
                api_key=os.getenv("DASHSCOPE_API_KEY"),
                streaming_mode=False,
            )
        elif asr == "bilibili":
            self.asr = BCutASR()
        else:
            raise ValueError(f"Unsupported ASR: {asr}")

    async def download_file(self, url: str, output_path: str, description: str) -> None:
        """Download a file from URL with progress reporting.

        Args:
            url: Download URL
            output_path: Local file path to save to
            description: Description for progress reporting

        Raises:
            VideoDownloadError: If download fails
        """
        try:
            download_id = await get_client().download_create(url, HEADERS)
            bytes_downloaded = 0
            total_bytes = get_client().download_content_length(download_id)

            with open(output_path, "wb") as file:
                while bytes_downloaded < total_bytes:
                    chunk = await get_client().download_chunk(download_id)
                    bytes_downloaded += file.write(chunk)

                    # Progress reporting
                    progress = f"{description} - {output_path} [{bytes_downloaded} / {total_bytes}]"
                    print(f"\r{progress}", end="")

            print()  # New line after progress
            logger.info(f"Successfully downloaded: {output_path}")

        except Exception as e:
            raise VideoDownloadError(f"Failed to download {url}: {e}")

    def _run_ffmpeg_command(self, command: List[str]) -> None:
        """Execute FFmpeg command safely.

        Args:
            command: FFmpeg command as list of arguments

        Raises:
            FFmpegError: If FFmpeg command fails
        """
        try:
            subprocess.run(
                command,
                check=True,
                capture_output=True,
                text=True
            )
            logger.debug(f"FFmpeg command succeeded: {' '.join(command)}")

        except subprocess.CalledProcessError as e:
            error_msg = f"FFmpeg command failed: {e.stderr}"
            logger.error(error_msg)
            raise FFmpegError(error_msg)
        except FileNotFoundError:
            error_msg = f"FFmpeg not found at: {FFMPEG_PATH}"
            logger.error(error_msg)
            raise FFmpegError(error_msg)

    def _process_flv_stream(self, flv_file: str, output_file: str) -> None:
        """Process FLV stream to MP4.

        Args:
            flv_file: Input FLV file path
            output_file: Output MP4 file path
        """
        command = [str(FFMPEG_PATH), "-i", flv_file, output_file]
        self._run_ffmpeg_command(command)

    def _merge_video_audio_streams(self, video_file: str, audio_file: str, output_file: str) -> None:
        """Merge separate video and audio streams.

        Args:
            video_file: Input video file path
            audio_file: Input audio file path
            output_file: Output merged file path
        """
        command = [
            str(FFMPEG_PATH),
            "-i", video_file,
            "-i", audio_file,
            "-vcodec", "copy",
            "-acodec", "copy",
            output_file
        ]
        self._run_ffmpeg_command(command)

    def _process_audio_file(self, audio_file: str, output_file: str, subfix: str = "opus") -> None:
        # ffmpeg -i input-video-file -ac 1 -ar 16000 -acodec libopus output-audio-file.opus
        if subfix == "opus":
            acodec = "libopus"
        else:
            acodec = subfix
        command = [
            str(FFMPEG_PATH),
            "-i", audio_file,
            "-ac", "1",
            "-ar", "16000",
            "-acodec", acodec,
            output_file
        ]
        self._run_ffmpeg_command(command)

    async def download_video(self, bvid, page_index: int = 0, output_filename: str = DEFAULT_OUTPUT_FILENAME) -> str:
        """Download video and return the output file path.

        Args:
            page_index: Video page index (for multi-part videos)
            output_filename: Output file name

        Returns:
            Path to the downloaded video file

        Raises:
            VideoDownloadError: If download or processing fails
        """
        try:
            # Get download URLs
            video_instance = video.Video(bvid, credential=self.credential)
            download_url_data = await video_instance.get_download_url(page_index)
            detector = video.VideoDownloadURLDataDetecter(data=download_url_data)
            streams = detector.detect_best_streams()

            if not streams:
                raise VideoDownloadError("No streams available for download")

            # Check stream type and process accordingly
            if detector.check_flv_mp4_stream():
                # FLV stream processing
                with temporary_files(FLV_TEMP_FILENAME) as temp_files:
                    await self.download_file(
                        streams[0].url,
                        temp_files[0],
                        "Downloading FLV stream"
                    )
                    self._process_flv_stream(temp_files[0], output_filename)
            else:
                # Separate video and audio streams
                if len(streams) < 2:
                    raise VideoDownloadError("Insufficient streams for video/audio separation")

                with temporary_files(VIDEO_TEMP_FILENAME, AUDIO_TEMP_FILENAME) as temp_files:
                    # Download video and audio streams
                    await self.download_file(
                        streams[0].url,
                        temp_files[0],
                        "Downloading video stream"
                    )
                    await self.download_file(
                        streams[1].url,
                        temp_files[1],
                        "Downloading audio stream"
                    )

                    # Merge streams
                    self._merge_video_audio_streams(
                        temp_files[0],
                        temp_files[1],
                        output_filename
                    )

            logger.info(f"Video successfully downloaded as: {output_filename}")
            return output_filename

        except Exception as e:
            if isinstance(e, (VideoDownloadError, FFmpegError)):
                raise
            raise VideoDownloadError(f"Unexpected error during video download: {e}")

    async def download_audio(self, bvid, page_index: int = 0, output_filename: str = DEFAULT_OUTPUT_FILENAME) -> str:
        """Download audio and return the output file path.
        """
        try:
            video_instance = video.Video(bvid, credential=self.credential)
            download_url_data = await video_instance.get_download_url(page_index)
            detector = video.VideoDownloadURLDataDetecter(data=download_url_data)
            streams = detector.detect_best_streams()
            if not streams:
                raise VideoDownloadError("No streams available for download")

            audio_stream = streams[1]
            subfix = output_filename.split(".")[-1]
            with temporary_files(AUDIO_TEMP_FILENAME) as temp_files:
                await self.download_file(
                    audio_stream.url,
                    temp_files[0],
                    "Downloading audio stream"
                )
                self._process_audio_file(temp_files[0], output_filename, subfix)
            return output_filename

        except Exception as e:
            if isinstance(e, (VideoDownloadError, FFmpegError)):
                raise
            raise VideoDownloadError(f"Unexpected error during audio download: {e}")

    async def download_subtitle(self, bvid, page_index: int = 0, output_filename: str = DEFAULT_OUTPUT_FILENAME) -> str:
        """Download subtitle and return the output file path.
        """
        try:
            video_instance = video.Video(bvid, credential=self.credential)
            cid = await video_instance.get_cid(page_index)
            player_info = await video_instance.get_player_info(cid=cid)
            json_files = player_info["subtitle"]["subtitles"]
            target_subtitle = None
            for subtitle in json_files:
                if subtitle["lan"] == "ai-zh" and subtitle["lan_doc"] == "中文（自动生成）":
                    target_subtitle = subtitle
                    break

            if target_subtitle:
                subtitle_url = target_subtitle["subtitle_url"]
                if not subtitle_url.startswith(('http://', 'https://')):
                    subtitle_url = f"https:{subtitle_url}"

                async with aiohttp.ClientSession() as session:
                    async with session.get(subtitle_url) as response:
                        subtitle_content = await response.json()
                        if "body" in subtitle_content:
                            subtitle_text = "".join(item["content"] for item in subtitle_content["body"])
                            subtitle_content = subtitle_text
                        to_return_text = subtitle_content
            else:
                logger.info(f"No subtitle available for download, ASR instead.")
                await self.download_audio(bvid, page_index, AUDIO_OUTPUT_FILENAME)

                subtitle_content = await self.asr.transcribe(AUDIO_OUTPUT_FILENAME, ["zh", "en"], file_format="mp3", sample_rate=16000)
                os.remove(AUDIO_OUTPUT_FILENAME)
                to_return_text = await self.parse_subtitle(subtitle_content)

            await self.save_subtitle(subtitle_content, output_filename)
            logger.info(f"Subtitle successfully downloaded as: {to_return_text}")
            return to_return_text

        except Exception as e:
            if isinstance(e, (VideoDownloadError, FFmpegError)):
                raise e
            raise VideoDownloadError(f"Unexpected error during subtitle download: {e}")

    async def save_subtitle(self, subtitle_content, output_filename: str = TRANSCRIPT_OUTPUT_FILENAME):
        """Save subtitle to file."""
        with open(output_filename, "w", encoding="utf-8") as f:
            json.dump(subtitle_content, f, ensure_ascii=False, indent=4)

    async def parse_subtitle(self, input_data):
        try:
            if isinstance(input_data, str):
                fixed_data = fix_json_format(input_data)
                data = json.loads(fixed_data)
            else:
                data = input_data
        except json.JSONDecodeError as e:
            logger.error(f"JSON 解析错误: {e}")
            return ""
        except Exception as e:
            logger.error(f"错误: {e}")
            return ""

        id_text_pairs = []
        if isinstance(data, list):
            for item in data:
                if "sentence_id" in item and "text" in item:
                    id_text_pairs.append((item["sentence_id"], item["text"]))
                if "stash" in item and isinstance(item["stash"], dict):
                    stash = item["stash"]
                    if "sentence_id" in stash and "text" in stash:
                        id_text_pairs.append((stash["sentence_id"], stash["text"]))

        seen_ids = set()
        unique_texts = []
        for sid, text in sorted(id_text_pairs, key=lambda x: x[0]):
            if sid not in seen_ids:
                seen_ids.add(sid)
                unique_texts.append(text)

        return "\n".join(unique_texts)


def create_credential() -> Credential:
    """Create Bilibili API credential from stored cookies.

    Returns:
        Configured Credential object

    Raises:
        ValueError: If required credentials are missing
    """
    if not all([SESSDATA, BILI_JCT, BUVID3]):
        raise ValueError("Missing required Bilibili credentials")

    return Credential(sessdata=SESSDATA, bili_jct=BILI_JCT, buvid3=BUVID3)


video_source_downloader = VideoSourceDownloader()
